import { RESOURCE_TYPE } from "@/enums";

/**
 * 资源类型
 */
export type ResourceTabType = "my" | "public";

/**
 * 资源项目接口
 */
export interface ResourceItem {
  id: string;
  name: string;
  type: RESOURCE_TYPE;
  grade: string;
  subject: string;
  source: string;
  updateTime: string;
  isStarred: boolean;
  isShared: boolean;
  difficulty?: "简单" | "较易" | "一般" | "较难" | "困难" | "未知";
  status?: "进行中" | "已完成" | "未开始";
  fileType?: "pdf" | "doc" | "xls" | "ppt" | "mp3" | "mp4" | "png" | "jpg" | "folder";
  fileSize?: string;
  creator?: {
    id: string;
    name: string;
    avatar?: string;
  };
  tags?: string[];
}

/**
 * 筛选条件接口
 */
export interface ResourceFilters {
  grade: string[];
  source: string[];
  subject: string[];
}

/**
 * 资源列表查询参数
 */
export interface ResourceListParams {
  type: ResourceTabType;
  keyword?: string;
  filters?: Partial<ResourceFilters>;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

/**
 * 资源列表响应
 */
export interface ResourceListResponse {
  items: ResourceItem[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * 筛选选项
 */
export interface FilterOption {
  label: string;
  value: string;
  count?: number;
}

/**
 * 筛选配置
 */
export interface FilterConfig {
  grade: FilterOption[];
  source: FilterOption[];
  subject: FilterOption[];
}
