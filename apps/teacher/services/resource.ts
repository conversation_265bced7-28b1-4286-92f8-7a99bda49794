import { ResourceListParams, ResourceListResponse, FilterConfig } from "@/types/resource";
import { r } from "../libs/axios";

/**
 * 获取资源列表
 */
export const getResourceList = (params: ResourceListParams) => {
  return r.get<ResourceListResponse>("/resource/list", {
    params,
  });
};

/**
 * 获取筛选配置
 */
export const getFilterConfig = () => {
  return r.get<FilterConfig>("/resource/filter-config");
};

/**
 * 上传资源文件
 */
export const uploadResource = (file: File, metadata?: Record<string, any>) => {
  const formData = new FormData();
  formData.append("file", file);
  if (metadata) {
    formData.append("metadata", JSON.stringify(metadata));
  }
  
  return r.post<{ id: string; url: string }>("/resource/upload", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

/**
 * 删除资源
 */
export const deleteResource = (resourceId: string) => {
  return r.delete(`/resource/${resourceId}`);
};

/**
 * 收藏/取消收藏资源
 */
export const toggleResourceStar = (resourceId: string, starred: boolean) => {
  return r.post(`/resource/${resourceId}/star`, { starred });
};

/**
 * 分享资源
 */
export const shareResource = (resourceId: string, shareSettings: Record<string, any>) => {
  return r.post(`/resource/${resourceId}/share`, shareSettings);
};
