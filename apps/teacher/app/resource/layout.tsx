import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/ui/tabs";
import { cn } from "@/utils";
import { useSignal } from "@preact-signals/safe-react";

export default function Layout({ children }: { children: React.ReactNode }) {
  const activeTab = useSignal("good");
  return (
    <div>
      <Tabs
        // value={activeTab}
        // onValueChange={(value) => {
        //   setActiveTab(value as "good" | "warning");
        // }}
        className={cn(
          `w-full gap-0 overflow-hidden rounded-b-lg rounded-t-[1.25rem]`
        )}
      >
        <div className="h-10">
          <div className="relative h-14">
            <DynamicRankingLeft
              className={cn(
                `object-top-left w-238 absolute inset-0 h-14 object-none`,
                activeTab === "good" ? "z-0" : "-z-10"
              )}
            />

            <DynamicRankingMiddle
              className={cn(
                `object-top-left w-238 absolute inset-0 h-14 object-none`,
                activeTab === "warning" ? "z-0" : "-z-10"
              )}
            />

            <DynamicRankingRight
              className={cn(
                `object-top-left w-238 absolute inset-0 h-14 object-none`,
                activeTab === "personal" ? "z-0" : "-z-10"
              )}
            />

            <TabsList
              className={cn(
                `flex h-10 justify-between gap-6 border-0 bg-transparent p-0`
              )}
            >
              <TabsTrigger
                value="good"
                className={cn(
                  `bg-transparent! shadow-[unset]! text-gray-3 data-[state=active]:text-gray-2 w-54.25 relative h-full flex-none cursor-pointer gap-1 rounded-none border-0 p-0 text-base/normal font-normal data-[state=active]:font-medium`,
                  activeTab === "personal" &&
                    "before:bg-line-3 before:h-3.75 before:absolute before:-right-3 before:w-px before:content-['']",
                  activeTab === "good" && ""
                )}
              >
                <span className="w-0.75 bg-green-1 rounded-xs h-3.5"></span>
                <span>
                  鼓励课堂表现（
                  {studentLatestBehaviorData?.praiseList.length || 0}人）
                </span>
              </TabsTrigger>

              <TabsTrigger
                value="warning"
                className={cn(
                  `bg-transparent! shadow-[unset]! text-gray-3 data-[state=active]:text-gray-2 w-47.75 relative h-full flex-none cursor-pointer gap-1 rounded-none border-0 p-0 text-base/normal font-normal data-[state=active]:font-medium`,
                  activeTab === "warning" && ""
                )}
              >
                <span className="w-0.75 bg-orange-1 rounded-xs h-3.5"></span>
                <span>
                  注意课堂表现（
                  {studentLatestBehaviorData?.attentionList.length || 0}人）
                </span>
              </TabsTrigger>

              <TabsTrigger
                value="personal"
                className={cn(
                  `bg-transparent! shadow-[unset]! text-gray-3 data-[state=active]:text-gray-2 w-47.75 relative h-full flex-none cursor-pointer gap-1 rounded-none border-0 p-0 text-base/normal font-normal data-[state=active]:font-medium`,
                  activeTab === "good" &&
                    "before:bg-line-3 before:h-3.75 before:absolute before:-left-3 before:w-px before:content-['']",
                  activeTab === "personal" && ""
                )}
              >
                <span className="w-0.75 bg-primary-2 rounded-xs h-3.5"></span>
                <span>
                  个人榜单
                  {/* （{studentLatestBehaviorData?.learningScores?.length || 0}人） */}
                </span>
              </TabsTrigger>
            </TabsList>

            <div className="bottom-5.5 absolute right-0">
              {match({ isInClass, activeTab })
                .with({ isInClass: true, activeTab: "good" }, () => {
                  return (
                    <Button
                      className="border-primary-5 bg-primary-6 text-gray-2 h-7 font-medium"
                      size="sm"
                      radius="full"
                      icon={<IcZan />}
                      disabled={praiseStudentRequest.loading}
                      onClick={() => {
                        const ids = studentLatestBehaviorData?.praiseList
                          ?.filter((s) => !s.isHandled)
                          ?.map((s) => s.studentId);

                        if (!ids?.length) {
                          return;
                        }

                        umeng.trackEvent(
                          UmengCategory.COURSE,
                          UmengCourseAction.CLASSROOM_REPORT_LIKE,
                          {
                            like_action: {
                              subject: subject,
                              job: isSubjectTeacher ? "任课教师" : "班主任",
                            },
                          }
                        );

                        praiseStudentRequest.run(ids);
                      }}
                    >
                      一键鼓励
                    </Button>
                  );
                })
                .with({ isInClass: true, activeTab: "warning" }, () => {
                  return (
                    <Button
                      className="border-primary-5 bg-primary-6 text-gray-2 h-7 font-medium"
                      size="sm"
                      radius="full"
                      icon={<IcBell />}
                      disabled={remindStudentRequest.loading}
                      onClick={() => {
                        const ids = studentLatestBehaviorData?.attentionList
                          ?.filter((s) => !s.isHandled)
                          ?.map((s) => s.studentId);

                        if (!ids?.length) {
                          return;
                        }

                        umeng.trackEvent(
                          UmengCategory.COURSE,
                          UmengCourseAction.CLASSROOM_REPORT_PUSH,
                          {
                            push_action: {
                              subject: subject,
                              job: isSubjectTeacher ? "任课教师" : "班主任",
                            },
                          }
                        );

                        remindStudentRequest.run(ids);
                      }}
                    >
                      一键提醒
                    </Button>
                  );
                })
                .otherwise(() => null)}
            </div>
          </div>
        </div>

        <div className="relative rounded-t-[1.25rem] bg-white px-5 pb-6 pt-4">
          <TabsContent value="good"></TabsContent>

          <TabsContent value="warning"></TabsContent>

          <TabsContent value="personal"></TabsContent>
        </div>
      </Tabs>
      {children}
    </div>
  );
}
