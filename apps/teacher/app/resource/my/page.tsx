"use client";

import { RESOURCE_TYPE } from "@/enums";
import { ResourceItem, ResourceTabType } from "@/types/resource";
import { Avatar, AvatarFallback, AvatarImage } from "@/ui/avatar";
import { Badge } from "@/ui/badge";
import { <PERSON><PERSON> } from "@/ui/button";
import { Checkbox } from "@/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/dropdown-menu";
import { InputSearch } from "@/ui/searchInput";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/ui/tabs";
import { cn } from "@/utils";
import {
  ChevronDown,
  FileText,
  Folder,
  Image,
  MoreHorizontal,
  Music,
  Plus,
  Star,
  Upload,
  Video,
} from "lucide-react";
import { useState } from "react";

// Mock 数据
const mockResourceData: ResourceItem[] = [
  {
    id: "1",
    name: "高三数学复习重点总结",
    type: RESOURCE_TYPE.RESOURCE_TYPE_OTHER,
    grade: "高一, 高二, 高三",
    subject: "数学",
    source: "教师上传",
    updateTime: "2025-01-30",
    isStarred: true,
    isShared: false,
    fileType: "pdf",
    difficulty: "一般",
    creator: {
      id: "teacher1",
      name: "张老师",
      avatar: "/avatars/teacher1.jpg",
    },
  },
  {
    id: "2",
    name: "高三数学复习重点总结",
    type: RESOURCE_TYPE.RESOURCE_TYPE_OTHER,
    grade: "高一, 高二",
    subject: "数学",
    source: "教师上传",
    updateTime: "2025-01-30",
    isStarred: false,
    isShared: true,
    fileType: "folder",
    difficulty: "较易",
  },
  {
    id: "3",
    name: "高三数学复习重点总结",
    type: RESOURCE_TYPE.RESOURCE_TYPE_OTHER,
    grade: "高一",
    subject: "数学",
    source: "教师上传",
    updateTime: "2025-01-30",
    isStarred: false,
    isShared: false,
    fileType: "xls",
    difficulty: "简单",
  },
  {
    id: "4",
    name: "高三数学复习重点总结",
    type: RESOURCE_TYPE.RESOURCE_TYPE_OTHER,
    grade: "高二",
    subject: "数学",
    source: "教师上传",
    updateTime: "2025-01-30",
    isStarred: false,
    isShared: false,
    fileType: "png",
    difficulty: "较难",
  },
  {
    id: "5",
    name: "高三数学复习重点总结",
    type: RESOURCE_TYPE.RESOURCE_TYPE_OTHER,
    grade: "高一",
    subject: "数学",
    source: "教师上传",
    updateTime: "2025-01-30",
    isStarred: false,
    isShared: false,
    fileType: "mp3",
    difficulty: "困难",
  },
  {
    id: "6",
    name: "高三数学复习重点总结",
    type: RESOURCE_TYPE.RESOURCE_TYPE_OTHER,
    grade: "高一",
    subject: "数学",
    source: "教师上传",
    updateTime: "2020-01-30",
    isStarred: false,
    isShared: false,
    fileType: "pdf",
    difficulty: "未知",
  },
  {
    id: "7",
    name: "高三数学复习重点总结",
    type: RESOURCE_TYPE.RESOURCE_TYPE_OTHER,
    grade: "高二",
    subject: "数学",
    source: "教师上传",
    updateTime: "2020-01-30",
    isStarred: false,
    isShared: false,
    fileType: "pdf",
  },
];

export default function ResourcePage() {
  const [activeTab, setActiveTab] = useState<ResourceTabType>("my");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  // const [filters, setFilters] = useState<ResourceFilters>({
  //   grade: [],
  //   source: [],
  //   subject: [],
  // });

  // 筛选选项
  const filterOptions = {
    grade: [
      { label: "高一", value: "高一" },
      { label: "高二", value: "高二" },
      { label: "高三", value: "高三" },
    ],
    source: [
      { label: "教师上传", value: "教师上传" },
      { label: "系统推荐", value: "系统推荐" },
      { label: "共享资源", value: "共享资源" },
    ],
    subject: [
      { label: "数学", value: "数学" },
      { label: "语文", value: "语文" },
      { label: "英语", value: "英语" },
    ],
  };

  const handleSearch = (value: string) => {
    setSearchKeyword(value);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(mockResourceData.map((item) => item.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (itemId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems((prev) => [...prev, itemId]);
    } else {
      setSelectedItems((prev) => prev.filter((id) => id !== itemId));
    }
  };

  const getFileIcon = (fileType?: string) => {
    switch (fileType) {
      case "pdf":
      case "doc":
        return <FileText className="h-4 w-4 text-red-500" />;
      case "folder":
        return <Folder className="h-4 w-4 text-yellow-500" />;
      case "xls":
        return <FileText className="h-4 w-4 text-green-500" />;
      case "png":
      case "jpg":
        return <Image className="h-4 w-4 text-purple-500" />;
      case "mp3":
        return <Music className="h-4 w-4 text-pink-500" />;
      case "mp4":
        return <Video className="h-4 w-4 text-blue-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case "简单":
        return "bg-blue-100 text-blue-800";
      case "较易":
        return "bg-green-100 text-green-800";
      case "一般":
        return "bg-yellow-100 text-yellow-800";
      case "较难":
        return "bg-orange-100 text-orange-800";
      case "困难":
        return "bg-red-100 text-red-800";
      case "未知":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* 主内容区域 */}
      <div className="flex flex-1 flex-col p-8">
        {/* 顶部标题和操作区 */}
        <div className="mb-6">
          <div className="mb-6 flex items-center justify-between">
            <div className="flex items-center gap-6">
              <h1 className="text-xl font-semibold text-gray-900">我的资源</h1>
              <div className="h-1 w-3 rounded bg-gray-300"></div>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm" className="gap-2">
                <Upload className="h-4 w-4" />
                上传文件
              </Button>
              <Button size="sm" className="gap-2">
                <Plus className="h-4 w-4" />
                新建文件夹
              </Button>
            </div>
          </div>

          {/* 标签页和搜索 */}
          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as ResourceTabType)}
          >
            <div className="flex items-center justify-between">
              <TabsList className="grid w-fit grid-cols-2">
                <TabsTrigger value="my">我的资源</TabsTrigger>
                <TabsTrigger value="public">公共资源</TabsTrigger>
              </TabsList>

              <div className="flex items-center gap-4">
                <InputSearch
                  placeholder="输入关键字搜索资源"
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  onSearch={handleSearch}
                  className="w-64"
                />
              </div>
            </div>

            {/* 筛选器 */}
            <div className="mt-4 flex items-center gap-4">
              <Select>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="年级" />
                  <ChevronDown className="h-4 w-4" />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.grade.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="来源" />
                  <ChevronDown className="h-4 w-4" />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.source.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="学科" />
                  <ChevronDown className="h-4 w-4" />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.subject.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <TabsContent value="my" className="mt-6">
              <div className="space-y-4">
                <ResourceTable
                  data={mockResourceData}
                  selectedItems={selectedItems}
                  onSelectAll={handleSelectAll}
                  onSelectItem={handleSelectItem}
                  getFileIcon={getFileIcon}
                  getDifficultyColor={getDifficultyColor}
                />
                <Pagination
                  currentPage={currentPage}
                  totalPages={Math.ceil(mockResourceData.length / pageSize)}
                  onPageChange={setCurrentPage}
                />
              </div>
            </TabsContent>

            <TabsContent value="public" className="mt-6">
              <div className="space-y-4">
                <ResourceTable
                  data={mockResourceData}
                  selectedItems={selectedItems}
                  onSelectAll={handleSelectAll}
                  onSelectItem={handleSelectItem}
                  getFileIcon={getFileIcon}
                  getDifficultyColor={getDifficultyColor}
                />
                <Pagination
                  currentPage={currentPage}
                  totalPages={Math.ceil(mockResourceData.length / pageSize)}
                  onPageChange={setCurrentPage}
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

// 资源表格组件
interface ResourceTableProps {
  data: ResourceItem[];
  selectedItems: string[];
  onSelectAll: (checked: boolean) => void;
  onSelectItem: (itemId: string, checked: boolean) => void;
  getFileIcon: (fileType?: string) => React.ReactNode;
  getDifficultyColor: (difficulty?: string) => string;
}

function ResourceTable({
  data,
  selectedItems,
  onSelectAll,
  onSelectItem,
  getFileIcon,
  getDifficultyColor,
}: ResourceTableProps) {
  const isAllSelected = data.length > 0 && selectedItems.length === data.length;

  return (
    <div className="rounded-lg border bg-white">
      <Table>
        <TableHeader>
          <TableRow className="border-b">
            <TableHead className="w-12">
              <Checkbox checked={isAllSelected} onCheckedChange={onSelectAll} />
            </TableHead>
            <TableHead>名称</TableHead>
            <TableHead>年级</TableHead>
            <TableHead>更新日期</TableHead>
            <TableHead>操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((item) => (
            <TableRow key={item.id} className="hover:bg-gray-50">
              <TableCell>
                <Checkbox
                  checked={selectedItems.includes(item.id)}
                  onCheckedChange={(checked) =>
                    onSelectItem(item.id, checked as boolean)
                  }
                />
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-3">
                  {getFileIcon(item.fileType)}
                  <div className="flex flex-col">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-900">
                        {item.name}
                      </span>
                      {item.isStarred && (
                        <Star className="h-4 w-4 fill-current text-yellow-500" />
                      )}
                    </div>
                    {item.creator && (
                      <div className="mt-1 flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={item.creator.avatar} />
                          <AvatarFallback>
                            {item.creator.name[0]}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm text-gray-500">
                          {item.creator.name}
                        </span>
                        {item.difficulty && (
                          <Badge
                            variant="secondary"
                            className={cn(
                              "text-xs",
                              getDifficultyColor(item.difficulty)
                            )}
                          >
                            {item.difficulty}
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </TableCell>
              <TableCell className="text-gray-600">{item.grade}</TableCell>
              <TableCell className="text-gray-600">{item.updateTime}</TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm">
                    打开
                  </Button>
                  <Button variant="ghost" size="sm">
                    分享
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>编辑</DropdownMenuItem>
                      <DropdownMenuItem>下载</DropdownMenuItem>
                      <DropdownMenuItem>复制链接</DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        删除
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

// 简单的分页组件
interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

function Pagination({
  currentPage,
  totalPages,
  onPageChange,
}: PaginationProps) {
  const pages = Array.from({ length: totalPages }, (_, i) => i + 1);

  return (
    <div className="flex items-center justify-between border-t bg-white px-4 py-3">
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-700">
          第 {currentPage} 页，共 {totalPages} 页
        </span>
      </div>

      <div className="flex items-center gap-1">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          ←
        </Button>

        {pages.map((page) => (
          <Button
            key={page}
            variant={page === currentPage ? "default" : "outline"}
            size="sm"
            onClick={() => onPageChange(page)}
            className="min-w-[32px]"
          >
            {page}
          </Button>
        ))}

        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          →
        </Button>
      </div>
    </div>
  );
}
